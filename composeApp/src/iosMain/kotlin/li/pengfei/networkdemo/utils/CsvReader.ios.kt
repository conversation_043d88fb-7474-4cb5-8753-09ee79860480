package li.pengfei.networkdemo.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import platform.Foundation.*

actual class CsvReader {
    actual suspend fun readCsvFromAssets(fileName: String): String = withContext(Dispatchers.Default) {
        try {
            val bundle = NSBundle.mainBundle
            val path = bundle.pathForResource(fileName.substringBeforeLast('.'), fileName.substringAfterLast('.'))
            
            if (path != null) {
                val content = NSString.stringWithContentsOfFile(
                    path = path,
                    encoding = NSUTF8StringEncoding,
                    error = null
                )
                content?.toString() ?: SampleData.SAMPLE_CSV
            } else {
                println("CSV file not found in bundle, using sample data")
                SampleData.SAMPLE_CSV
            }
        } catch (e: Exception) {
            println("Error reading CSV file: ${e.message}")
            SampleData.SAMPLE_CSV
        }
    }

    actual suspend fun readCsvFromFile(filePath: String): String? = withContext(Dispatchers.Default) {
        try {
            val content = NSString.stringWithContentsOfFile(
                path = filePath,
                encoding = NSUTF8StringEncoding,
                error = null
            )
            content?.toString()
        } catch (e: Exception) {
            println("Error reading file: ${e.message}")
            null
        }
    }
}

@Composable
fun rememberCsvReader(): CsvReader {
    return remember { CsvReader() }
}
