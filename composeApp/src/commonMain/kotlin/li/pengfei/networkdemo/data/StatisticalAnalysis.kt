package li.pengfei.networkdemo.data

import kotlin.math.*

/**
 * Statistical analysis functions for network data
 */
class StatisticalAnalysis {
    
    /**
     * Calculate histogram data from a list of values
     */
    fun calculateHistogram(values: List<Double>, bins: Int = 20): List<Pair<Double, Int>> {
        if (values.isEmpty()) return emptyList()
        
        val minValue = values.minOrNull() ?: 0.0
        val maxValue = values.maxOrNull() ?: 0.0
        val range = maxValue - minValue
        
        if (range == 0.0) {
            return listOf(Pair(minValue, values.size))
        }
        
        val binWidth = range / bins
        val histogram = mutableMapOf<Int, Int>()
        
        values.forEach { value ->
            val binIndex = min(bins - 1, ((value - minValue) / binWidth).toInt())
            histogram[binIndex] = histogram.getOrDefault(binIndex, 0) + 1
        }
        
        return (0 until bins).map { binIndex ->
            val binCenter = minValue + (binIndex + 0.5) * binWidth
            val count = histogram.getOrDefault(binIndex, 0)
            Pair(binCenter, count)
        }
    }
    
    /**
     * Calculate Probability Density Function (PDF) using kernel density estimation
     */
    fun calculatePDF(values: List<Double>, points: Int = 100): List<Pair<Double, Double>> {
        if (values.isEmpty()) return emptyList()
        
        val minValue = values.minOrNull() ?: 0.0
        val maxValue = values.maxOrNull() ?: 0.0
        val range = maxValue - minValue
        
        if (range == 0.0) {
            return listOf(Pair(minValue, 1.0))
        }
        
        // Calculate bandwidth using Silverman's rule of thumb
        val n = values.size
        val std = calculateStandardDeviation(values)
        val bandwidth = 1.06 * std * n.toDouble().pow(-1.0/5.0)
        
        val step = range / (points - 1)
        
        return (0 until points).map { i ->
            val x = minValue + i * step
            val density = values.sumOf { value ->
                gaussianKernel((x - value) / bandwidth)
            } / (n * bandwidth)
            
            Pair(x, density)
        }
    }
    
    /**
     * Calculate Cumulative Distribution Function (CDF)
     */
    fun calculateCDF(values: List<Double>, points: Int = 100): List<Pair<Double, Double>> {
        if (values.isEmpty()) return emptyList()
        
        val sortedValues = values.sorted()
        val minValue = sortedValues.first()
        val maxValue = sortedValues.last()
        val range = maxValue - minValue
        
        if (range == 0.0) {
            return listOf(Pair(minValue, 1.0))
        }
        
        val step = range / (points - 1)
        
        return (0 until points).map { i ->
            val x = minValue + i * step
            val cumulativeProbability = sortedValues.count { it <= x }.toDouble() / sortedValues.size
            Pair(x, cumulativeProbability)
        }
    }
    
    /**
     * Gaussian kernel function for KDE
     */
    private fun gaussianKernel(u: Double): Double {
        return exp(-0.5 * u * u) / sqrt(2 * PI)
    }
    
    /**
     * Calculate standard deviation
     */
    private fun calculateStandardDeviation(values: List<Double>): Double {
        if (values.isEmpty()) return 0.0
        
        val mean = values.average()
        val variance = values.sumOf { (it - mean).pow(2) } / values.size
        return sqrt(variance)
    }
    
    /**
     * Generate statistical data for chart visualization
     */
    fun generateStatisticalData(chartData: CategorizedChartData): Map<String, StatisticalData> {
        val result = mutableMapOf<String, StatisticalData>()
        
        // Group data by category
        val groupedData = chartData.dataPoints.groupBy { it.category }
        
        groupedData.forEach { (category, points) ->
            val values = points.map { it.y.toDouble() }
            
            if (values.isNotEmpty()) {
                val histogram = calculateHistogram(values)
                val pdf = calculatePDF(values)
                val cdf = calculateCDF(values)
                
                result[category] = StatisticalData(
                    values = values,
                    histogram = histogram,
                    pdf = pdf,
                    cdf = cdf
                )
            }
        }
        
        return result
    }
    
    /**
     * Convert statistical data to chart data points for visualization
     */
    fun statisticalDataToChartPoints(
        statisticalData: Map<String, StatisticalData>,
        type: StatisticalType
    ): CategorizedChartData {
        val dataPoints = mutableListOf<ChartDataPoint>()
        
        statisticalData.forEach { (category, data) ->
            val points = when (type) {
                StatisticalType.HISTOGRAM -> data.histogram.map { (x, y) ->
                    ChartDataPoint(x.toString(), y.toFloat(), category)
                }
                StatisticalType.PDF -> data.pdf.map { (x, y) ->
                    ChartDataPoint(x.toString(), y.toFloat(), category)
                }
                StatisticalType.CDF -> data.cdf.map { (x, y) ->
                    ChartDataPoint(x.toString(), y.toFloat(), category)
                }
            }
            dataPoints.addAll(points)
        }
        
        val title = when (type) {
            StatisticalType.HISTOGRAM -> "Histogram"
            StatisticalType.PDF -> "Probability Density Function (PDF)"
            StatisticalType.CDF -> "Cumulative Distribution Function (CDF)"
        }
        
        val yAxisLabel = when (type) {
            StatisticalType.HISTOGRAM -> "Frequency"
            StatisticalType.PDF -> "Density"
            StatisticalType.CDF -> "Cumulative Probability"
        }
        
        return CategorizedChartData(
            title = title,
            xAxisLabel = "Value",
            yAxisLabel = yAxisLabel,
            dataPoints = dataPoints,
            categories = statisticalData.keys.toSet()
        )
    }
}

/**
 * Enumeration for statistical analysis types
 */
enum class StatisticalType {
    HISTOGRAM,
    PDF,
    CDF
}

/**
 * Extension functions for easier statistical analysis
 */
fun CategorizedChartData.toStatisticalData(): Map<String, StatisticalData> {
    return StatisticalAnalysis().generateStatisticalData(this)
}

fun Map<String, StatisticalData>.toChartData(type: StatisticalType): CategorizedChartData {
    return StatisticalAnalysis().statisticalDataToChartPoints(this, type)
}
