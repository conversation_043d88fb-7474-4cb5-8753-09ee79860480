package li.pengfei.networkdemo.data

import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.format.DateTimeFormat

/**
 * Data class representing a single network measurement record
 */
data class NetworkRecord(
    val time: LocalDateTime,
    val deviceState: DeviceState,
    val ulThroughput: Double, // UL_TPut(Kb/s)
    val dlThroughput: Double, // DL_TPut(Kb/s)
    val cells: List<CellData>
)

/**
 * Device state enumeration
 */
enum class DeviceState(val value: Int, val displayName: String) {
    FREE(1, "Free"),
    HEAD(2, "Head"),
    BODY(3, "Body");
    
    companion object {
        fun fromString(value: String): DeviceState {
            return when (value.lowercase()) {
                "free" -> FREE
                "head" -> HEAD
                "body" -> BODY
                else -> FREE
            }
        }
    }
}

/**
 * Network technology enumeration
 */
enum class NetworkTechnology(val displayName: String) {
    LTE("LTE"),
    NR("NR"),
    UNKNOWN("Unknown");
    
    companion object {
        fun fromString(value: String): NetworkTechnology {
            return when (value.uppercase()) {
                "LTE" -> LTE
                "NR" -> NR
                else -> UNKNOWN
            }
        }
    }
}

/**
 * Data class representing cell information
 */
data class CellData(
    val cellId: Int, // Cell1, Cell2, etc.
    val rat: NetworkTechnology,
    val band: String,
    val dlChannel: Long?,
    val ulChannel: Long?,
    val rsrpValues: List<Double>, // RSRP0, RSRP1, RSRP2, RSRP3
    val txPowerValues: List<Double> // TXPower0, TXPower1, etc.
)

/**
 * Data class for chart data points
 */
data class ChartDataPoint(
    val x: String, // Time as formatted string from NetworkRecord.time
    val y: Float, // The measured value
    val category: String = "", // For grouping (e.g., RAT, Band)
    val subcategory: String = "" // For sub-grouping (e.g., specific band within RAT)
)

/**
 * Data class for statistical analysis results
 */
data class StatisticalData(
    val values: List<Double>,
    val histogram: List<Pair<Double, Int>>, // (bin center, count)
    val pdf: List<Pair<Double, Double>>, // (value, probability density)
    val cdf: List<Pair<Double, Double>> // (value, cumulative probability)
)

/**
 * Data class for organizing chart data by categories
 */
data class CategorizedChartData(
    val title: String,
    val xAxisLabel: String,
    val yAxisLabel: String,
    val dataPoints: List<ChartDataPoint>,
    val categories: Set<String> = emptySet(),
    val subcategories: Set<String> = emptySet()
)
