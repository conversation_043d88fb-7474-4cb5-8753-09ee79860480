package li.pengfei.networkdemo.ui.charts

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.multiplatform.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.multiplatform.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.multiplatform.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.multiplatform.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.multiplatform.cartesian.data.columnSeries
import com.patrykandpatrick.vico.multiplatform.cartesian.data.lineSeries
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.rememberColumnCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.multiplatform.cartesian.rememberCartesianChart
import li.pengfei.networkdemo.data.CategorizedChartData
import li.pengfei.networkdemo.data.StatisticalType

/**
 * Histogram chart component using Vico
 */
@Composable
fun HistogramChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    val modelProducer = remember { CartesianChartModelProducer() }

    LaunchedEffect(chartData) {
        updateHistogramModel(modelProducer, chartData)
    }

    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        Text(
            text = "Histogram - ${chartData.title}",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        // Chart
        CartesianChartHost(
            chart = rememberCartesianChart(
                rememberColumnCartesianLayer(),
                startAxis = VerticalAxis.rememberStart(),
                bottomAxis = HorizontalAxis.rememberBottom(),
            ),
            modelProducer = modelProducer,
            modifier = Modifier.fillMaxWidth().height(400.dp)
        )

        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticalLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * Update histogram chart model with binned data
 */
private suspend fun updateHistogramModel(
    modelProducer: CartesianChartModelProducer,
    chartData: CategorizedChartData
) {
    if (chartData.dataPoints.isEmpty()) return

    // Create histogram bins
    val values = chartData.dataPoints.map { it.y.toDouble() }
    val min = values.minOrNull() ?: 0.0
    val max = values.maxOrNull() ?: 0.0
    val binCount = 20
    val binWidth = if (max > min) (max - min) / binCount else 1.0

    val bins = Array(binCount) { 0 }
    values.forEach { value ->
        val binIndex = if (binWidth > 0) {
            ((value - min) / binWidth).toInt().coerceIn(0, binCount - 1)
        } else {
            0
        }
        bins[binIndex]++
    }

    modelProducer.runTransaction {
        columnSeries {
            val xValues = (0 until binCount).map { it.toDouble() }
            val yValues = bins.map { it.toDouble() }
            series(xValues, yValues)
        }
    }
}

/**
 * PDF chart component using Vico
 */
@Composable
fun PDFChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    val modelProducer = remember { CartesianChartModelProducer() }

    LaunchedEffect(chartData) {
        updatePDFModel(modelProducer, chartData)
    }

    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        Text(
            text = "PDF - ${chartData.title}",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        // Chart
        CartesianChartHost(
            chart = rememberCartesianChart(
                rememberLineCartesianLayer(),
                startAxis = VerticalAxis.rememberStart(),
                bottomAxis = HorizontalAxis.rememberBottom(),
            ),
            modelProducer = modelProducer,
            modifier = Modifier.fillMaxWidth().height(400.dp)
        )

        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticalLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * Update PDF chart model with probability density function
 */
private suspend fun updatePDFModel(
    modelProducer: CartesianChartModelProducer,
    chartData: CategorizedChartData
) {
    if (chartData.dataPoints.isEmpty()) return

    val values = chartData.dataPoints.map { it.y.toDouble() }
    val mean = values.average()
    val std = kotlin.math.sqrt(values.map { (it - mean).let { diff -> diff * diff } }.average())

    if (std == 0.0) return

    val min = values.minOrNull() ?: 0.0
    val max = values.maxOrNull() ?: 0.0
    val range = max - min
    val step = range / 100

    modelProducer.runTransaction {
        lineSeries {
            val xValues = mutableListOf<Double>()
            val yValues = mutableListOf<Double>()

            // Use integer-based iteration to avoid precision issues
            val numPoints = 100
            for (i in 0..numPoints) {
                val x = min + (range * i / numPoints)
                // Round x to 4 decimal places to avoid precision issues
                val roundedX = kotlin.math.round(x * 10000.0) / 10000.0

                val pdf = (1.0 / (std * kotlin.math.sqrt(2 * kotlin.math.PI))) *
                         kotlin.math.exp(-0.5 * ((roundedX - mean) / std).let { it * it })
                xValues.add(roundedX)
                yValues.add(pdf)
            }

            series(xValues, yValues)
        }
    }
}

/**
 * CDF chart component using Vico
 */
@Composable
fun CDFChart(
    chartData: CategorizedChartData,
    modifier: Modifier = Modifier
) {
    val modelProducer = remember { CartesianChartModelProducer() }

    LaunchedEffect(chartData) {
        updateCDFModel(modelProducer, chartData)
    }

    Column(
        modifier = modifier.fillMaxSize().padding(16.dp)
    ) {
        Text(
            text = "CDF - ${chartData.title}",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.align(Alignment.CenterHorizontally).padding(bottom = 16.dp)
        )

        // Chart
        CartesianChartHost(
            chart = rememberCartesianChart(
                rememberLineCartesianLayer(),
                startAxis = VerticalAxis.rememberStart(),
                bottomAxis = HorizontalAxis.rememberBottom(),
            ),
            modelProducer = modelProducer,
            modifier = Modifier.fillMaxWidth().height(400.dp)
        )

        if (chartData.categories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            StatisticalLegend(
                categories = chartData.categories.toList(),
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

/**
 * Update CDF chart model with cumulative distribution function
 */
private suspend fun updateCDFModel(
    modelProducer: CartesianChartModelProducer,
    chartData: CategorizedChartData
) {
    if (chartData.dataPoints.isEmpty()) return

    val values = chartData.dataPoints.map { it.y.toDouble() }.sorted()
    val n = values.size

    modelProducer.runTransaction {
        lineSeries {
            // Round x values to 4 decimal places to avoid precision issues
            val xValues = values.map { kotlin.math.round(it * 10000.0) / 10000.0 }
            val yValues = values.mapIndexed { index, _ -> (index + 1).toDouble() / n }
            series(xValues, yValues)
        }
    }
}

/**
 * Statistical chart legend component
 */
@Composable
private fun StatisticalLegend(
    categories: List<String>,
    modifier: Modifier = Modifier
) {
    val colors = remember {
        listOf(
            Color(0xFF2196F3), // Blue
            Color(0xFF4CAF50), // Green
            Color(0xFFFF9800), // Orange
            Color(0xFF9C27B0), // Purple
            Color(0xFFF44336), // Red
            Color(0xFF00BCD4), // Cyan
            Color(0xFFFFEB3B), // Yellow
            Color(0xFF795548)  // Brown
        )
    }
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(categories.size) { index ->
            val category = categories[index]
            val color = colors[index % colors.size]
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(color, CircleShape)
                )
                Text(
                    text = category,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}

/**
 * Combined statistical charts view
 */
@Composable
fun StatisticalChartsView(
    originalChartData: CategorizedChartData,
    statisticalType: StatisticalType,
    modifier: Modifier = Modifier
) {
    when (statisticalType) {
        StatisticalType.HISTOGRAM -> {
            HistogramChart(
                chartData = originalChartData,
                modifier = modifier
            )
        }
        StatisticalType.PDF -> {
            PDFChart(
                chartData = originalChartData,
                modifier = modifier
            )
        }
        StatisticalType.CDF -> {
            CDFChart(
                chartData = originalChartData,
                modifier = modifier
            )
        }
    }
}
