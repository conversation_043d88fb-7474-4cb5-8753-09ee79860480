package li.pengfei.networkdemo.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import li.pengfei.networkdemo.data.*
import li.pengfei.networkdemo.ui.charts.*
import li.pengfei.networkdemo.utils.CsvDataState
import li.pengfei.networkdemo.utils.rememberFilePicker

/**
 * Main application composable for network data visualization
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NetworkDataVisualizationApp(
    csvDataState: CsvDataState,
    modifier: Modifier = Modifier
) {
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var selectedChartType by remember { mutableIntStateOf(0) }
    var networkData by remember { mutableStateOf<List<NetworkRecord>>(emptyList()) }
    var isParsingData by remember { mutableStateOf(false) }
    var parseError by remember { mutableStateOf<String?>(null) }

    // Parse CSV data when content changes
    LaunchedEffect(csvDataState.csvContent) {
        if (csvDataState.csvContent.isNotEmpty()) {
            try {
                isParsingData = true
                parseError = null
                val parser = CsvParser()
                networkData = parser.parseNetworkData(csvDataState.csvContent)
            } catch (e: Exception) {
                parseError = "Error parsing CSV: ${e.message}"
                networkData = emptyList()
            } finally {
                isParsingData = false
            }
        }
    }

    Column(modifier = modifier.fillMaxSize()) {
        // Top App Bar
        TopAppBar(
            title = { Text("Network Data Visualization") }
        )

        // File controls section
        FileControlsSection(csvDataState = csvDataState)

        // Show loading or error states
        when {
            csvDataState.isLoading || isParsingData -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        CircularProgressIndicator()
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = if (csvDataState.isLoading) "Loading CSV data..." else "Parsing data...",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            csvDataState.error != null || parseError != null -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = csvDataState.error ?: parseError ?: "Unknown error",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = {
                                // Reset to default data - this would need coroutine scope
                            }
                        ) {
                            Text("Reset to Default")
                        }
                    }
                }
            }

            networkData.isEmpty() -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            else -> {
                // File status indicator
                FileStatusIndicator(csvDataState = csvDataState)

                // Tab Row
                TabRow(selectedTabIndex = selectedTabIndex) {
                    listOf("Scatter", "Histogram", "PDF", "CDF").forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTabIndex == index,
                            onClick = { selectedTabIndex = index },
                            text = { Text(title) }
                        )
                    }
                }

                // Chart type selector (only for scatter plots)
                if (selectedTabIndex == 0) {
                    ChartTypeSelector(
                        selectedChartType = selectedChartType,
                        onChartTypeSelected = { newType: Int -> selectedChartType = newType },
                        modifier = Modifier.padding(16.dp)
                    )
                }

                // Content based on selected tab
                when (selectedTabIndex) {
                    0 -> ScatterPlotsContent(
                        networkData = networkData,
                        selectedChartType = selectedChartType,
                        modifier = Modifier.fillMaxSize()
                    )

                    1 -> StatisticalContent(
                        networkData = networkData,
                        statisticalType = StatisticalType.HISTOGRAM,
                        modifier = Modifier.fillMaxSize()
                    )

                    2 -> StatisticalContent(
                        networkData = networkData,
                        statisticalType = StatisticalType.PDF,
                        modifier = Modifier.fillMaxSize()
                    )

                    3 -> StatisticalContent(
                        networkData = networkData,
                        statisticalType = StatisticalType.CDF,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }

}

/**
 * Chart type selector for scatter plots
 */
@Composable
fun ChartTypeSelector(
    selectedChartType: Int,
    onChartTypeSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val chartTypes = listOf(
        "Device State",
        "RSRP",
        "Tx Power",
        "Bands",
        "Channels",
        "UL Throughput",
        "DL Throughput"
    )

    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(chartTypes.size) { index ->
            FilterChip(
                selected = selectedChartType == index,
                onClick = { onChartTypeSelected(index) },
                label = { Text(chartTypes[index]) }
            )
        }
    }
}

/**
 * File controls section for the top app bar
 */
@Composable
fun FileControlsSection(
    csvDataState: CsvDataState,
    modifier: Modifier = Modifier
) {
    val filePicker = rememberFilePicker { filePath ->
        csvDataState.loadFromFile(filePath)
    }

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // File selection button
        OutlinedButton(
            onClick = { filePicker() },
            enabled = !csvDataState.isLoading
        ) {
            Text("Select File")
        }

        // Reset to default button
        Button(
            onClick = { csvDataState.resetToDefault() },
            enabled = !csvDataState.isLoading
        ) {
            Text("Default")
        }
    }
}

/**
 * File status indicator
 */
@Composable
fun FileStatusIndicator(
    csvDataState: CsvDataState,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.padding(horizontal = 16.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        when {
            csvDataState.isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Loading...",
                    style = MaterialTheme.typography.bodySmall
                )
            }
            csvDataState.error != null -> {
                Text(
                    text = "Error: ${csvDataState.error}",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall
                )
            }
            else -> {
                Text(
                    text = "Data loaded successfully",
                    color = MaterialTheme.colorScheme.primary,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        }
    }
}


/**
 * Scatter plots content
 */
@Composable
fun ScatterPlotsContent(
    networkData: List<NetworkRecord>,
    selectedChartType: Int,
    modifier: Modifier = Modifier
) {
    val processor = remember { NetworkDataProcessor() }

    val chartData = remember(networkData, selectedChartType) {
        when (selectedChartType) {
            0 -> processor.getDeviceStateData(networkData)
            1 -> processor.getRsrpData(networkData)
            2 -> processor.getTxPowerData(networkData)
            3 -> processor.getBandData(networkData)
            4 -> processor.getChannelData(networkData)
            5 -> processor.getThroughputData(networkData, isUplink = true)
            6 -> processor.getThroughputData(networkData, isUplink = false)
            else -> processor.getDeviceStateData(networkData)
        }
    }

    Box(modifier = modifier) {
        when (selectedChartType) {
            0 -> DeviceStateChart(chartData = chartData)
            1 -> RsrpChart(chartData = chartData)
            2 -> TxPowerChart(chartData = chartData)
            3 -> BandChart(chartData = chartData)
            4 -> ChannelChart(chartData = chartData)
            5 -> ThroughputChart(chartData = chartData, isUplink = true)
            6 -> ThroughputChart(chartData = chartData, isUplink = false)
        }
    }
}

/**
 * Statistical content for histogram, PDF, and CDF
 */
@Composable
fun StatisticalContent(
    networkData: List<NetworkRecord>,
    statisticalType: StatisticalType,
    modifier: Modifier = Modifier
) {
    val processor = remember { NetworkDataProcessor() }
    val statisticalAnalysis = remember { StatisticalAnalysis() }

    // Get all chart types for statistical analysis
    val allChartData = remember(networkData) {
        listOf(
            "Device State" to processor.getDeviceStateData(networkData),
            "RSRP" to processor.getRsrpData(networkData),
            "Tx Power" to processor.getTxPowerData(networkData),
            "UL Throughput" to processor.getThroughputData(networkData, isUplink = true),
            "DL Throughput" to processor.getThroughputData(networkData, isUplink = false)
        )
    }

    LazyColumn(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        items(allChartData) { (title, chartData) ->
            val statisticalData = remember(chartData) {
                statisticalAnalysis.generateStatisticalData(chartData)
            }

            val statisticalChartData = remember(statisticalData, statisticalType) {
                statisticalAnalysis.statisticalDataToChartPoints(statisticalData, statisticalType)
            }

            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                StatisticalChartsView(
                    originalChartData = statisticalChartData.copy(title = title),
                    statisticalType = statisticalType,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}
