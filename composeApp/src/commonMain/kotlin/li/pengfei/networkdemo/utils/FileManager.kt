package li.pengfei.networkdemo.utils

import androidx.compose.runtime.*
import org.jetbrains.compose.resources.ExperimentalResourceApi
import networkdemo.composeapp.generated.resources.Res
import kotlinx.coroutines.launch

/**
 * File manager for handling CSV file loading
 */
class FileManager {
    private val csvReader = CsvReader()
    
    /**
     * Load default CSV data from resources
     */
    @OptIn(ExperimentalResourceApi::class)
    suspend fun loadDefaultCsvData(): String {
        return try {
            // Try to read from resources first
            Res.readBytes("files/data.csv").decodeToString()
        } catch (e: Exception) {
            println("Failed to load default CSV file: ${e.message}")
            // Fallback to sample data
            SampleData.SAMPLE_CSV
        }
    }
    
    /**
     * Load CSV data from a user-selected file
     */
    suspend fun loadCsvFromFile(filePath: String): String? {
        return try {
            csvReader.readCsvFromFile(filePath)
        } catch (e: Exception) {
            println("Failed to load CSV file from $filePath: ${e.message}")
            null
        }
    }
}

/**
 * Composable state holder for CSV data management
 */
@Composable
fun rememberCsvDataState(): CsvDataState {
    val fileManager = remember { FileManager() }
    var csvContent by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(true) }
    var error by remember { mutableStateOf<String?>(null) }
    val scope = rememberCoroutineScope()
    
    // Load default data on first composition
    LaunchedEffect(Unit) {
        try {
            isLoading = true
            error = null
            csvContent = fileManager.loadDefaultCsvData()
        } catch (e: Exception) {
            error = "Failed to load default data: ${e.message}"
            csvContent = SampleData.SAMPLE_CSV
        } finally {
            isLoading = false
        }
    }
    
    return CsvDataState(
        csvContent = csvContent,
        isLoading = isLoading,
        error = error,
        loadFromFile = { filePath ->
            scope.launch {
                try {
                    isLoading = true
                    error = null
                    val content = fileManager.loadCsvFromFile(filePath)
                    if (content != null) {
                        csvContent = content
                    } else {
                        error = "Failed to load file: $filePath"
                    }
                } catch (e: Exception) {
                    error = "Error loading file: ${e.message}"
                } finally {
                    isLoading = false
                }
            }
        },
        resetToDefault = {
            scope.launch {
                try {
                    isLoading = true
                    error = null
                    csvContent = fileManager.loadDefaultCsvData()
                } catch (e: Exception) {
                    error = "Failed to reset to default: ${e.message}"
                    csvContent = SampleData.SAMPLE_CSV
                } finally {
                    isLoading = false
                }
            }
        }
    )
}

/**
 * State holder for CSV data
 */
data class CsvDataState(
    val csvContent: String,
    val isLoading: Boolean,
    val error: String?,
    val loadFromFile: (String) -> Unit,
    val resetToDefault: () -> Unit
)
