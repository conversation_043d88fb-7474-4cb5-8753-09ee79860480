package li.pengfei.networkdemo.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch
import javax.swing.JFileChooser
import javax.swing.filechooser.FileNameExtensionFilter

/**
 * JVM-specific file picker implementation using Swing
 */
@Composable
actual fun rememberFilePicker(
    onFileSelected: suspend (String) -> Unit
): () -> Unit {
    val scope = rememberCoroutineScope()
    
    return {
        scope.launch {
            val fileChooser = JFileChooser().apply {
                fileSelectionMode = JFileChooser.FILES_ONLY
                isMultiSelectionEnabled = false
                fileFilter = FileNameExtensionFilter("CSV Files", "csv", "txt")
            }
            
            val result = fileChooser.showOpenDialog(null)
            if (result == JFileChooser.APPROVE_OPTION) {
                val selectedFile = fileChooser.selectedFile
                onFileSelected(selectedFile.absolutePath)
            }
        }
    }
}
