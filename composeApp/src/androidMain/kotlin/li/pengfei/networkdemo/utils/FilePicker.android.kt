package li.pengfei.networkdemo.utils

import android.content.Intent
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import kotlinx.coroutines.launch

/**
 * Android-specific file picker implementation
 */
@Composable
actual fun rememberFilePicker(
    onFileSelected: suspend (String) -> Unit
): () -> Unit {
    val scope = rememberCoroutineScope()
    
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        result.data?.data?.let { uri ->
            scope.launch {
                onFileSelected(uri.toString())
            }
        }
    }
    
    return {
        val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "text/*"
            addCategory(Intent.CATEGORY_OPENABLE)
            putExtra(Intent.EXTRA_MIME_TYPES, arrayOf("text/csv", "text/plain"))
        }
        launcher.launch(intent)
    }
}
